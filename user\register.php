<?php
/**
 * User Registration Page
 * MLM Binary Plan System
 */

require_once '../includes/header.php';
require_once '../includes/BinaryTree.php';

// Redirect if already logged in
if (isLoggedIn('user')) {
    Response::redirect('dashboard.php');
}

$error = '';
$success = '';
$sponsorId = $_GET['sponsor'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    verifyCsrfToken();
    
    // Validate input
    $validator = new Validator($_POST);
    $validator->required('full_name', 'Full name is required')
             ->required('email', 'Email is required')
             ->email('email')
            //  ->unique('email', 'users', 'email')
             ->required('phone', 'Phone number is required')
             ->phone('phone')
            //  ->unique('phone', 'users', 'phone')
             ->required('password', 'Password is required')
             ->minLength('password', 6)
             ->required('confirm_password', 'Confirm password is required')
             ->matches('confirm_password', 'password', 'Passwords do not match')
             ->required('sponsor_id', 'Sponsor ID is required')
             ->exists('sponsor_id', 'users', 'user_id', 'Invalid sponsor ID')
             ->required('placement_side', 'Placement side is required');
    
    if ($validator->passes()) {
        try {
            $db = Database::getInstance();
            $db->beginTransaction();
            
            // Generate unique user ID and username
            $userId = generateUserId();
            $username = strtolower(str_replace(' ', '', $_POST['full_name'])) . mt_rand(100, 999);
            
            // Check if username already exists and modify if needed
            $checkStmt = $db->prepare("SELECT COUNT(*) FROM users WHERE username = ?");
            $checkStmt->execute([$username]);
            if ($checkStmt->fetchColumn() > 0) {
                $username .= mt_rand(1000, 9999);
            }
            
            // ⚠️ CRITICAL SECURITY WARNING ⚠️
            // STORING PASSWORDS IN PLAIN TEXT IS EXTREMELY DANGEROUS!
            // This exposes all user passwords and violates security standards.
            // Original secure implementation used: password_hash($_POST['password'], PASSWORD_BCRYPT)
            $hashedPassword = $_POST['password']; // PLAIN TEXT - SECURITY RISK!

            // Get default franchise ID (first active franchise)
            $franchiseStmt = $db->prepare("SELECT id FROM franchise WHERE status = 'active' ORDER BY id LIMIT 1");
            $franchiseStmt->execute();
            $defaultFranchise = $franchiseStmt->fetch();
            $franchiseId = $defaultFranchise ? $defaultFranchise['id'] : null;

            // Insert user
            $userStmt = $db->prepare("INSERT INTO users (user_id, username, email, password, full_name, phone, address, sponsor_id, franchise_id, placement_side, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $userStmt->execute([
                $userId,
                $username,
                $_POST['email'],
                $hashedPassword,
                $_POST['full_name'],
                $_POST['phone'],
                $_POST['address'] ?? '',
                $_POST['sponsor_id'],
                $franchiseId,
                $_POST['placement_side'],
                'active'
            ]);
            
            // Create wallet for user
            $walletStmt = $db->prepare("INSERT INTO wallet (user_id, balance, total_earned, total_withdrawn) VALUES (?, 0.00, 0.00, 0.00)");
            $walletStmt->execute([$userId]);
            
            // Add to binary tree (without transaction since we're already in one)
            $binaryTree = new BinaryTree();
            $treeResult = $binaryTree->addUser($userId, $_POST['sponsor_id'], $_POST['placement_side'], false);

            if (!$treeResult) {
                throw new Exception("Failed to add user to binary tree");
            }
            
            $db->commit();
            
            $success = "Registration successful! Your User ID is: <strong>{$userId}</strong> and Username is: <strong>{$username}</strong>. Please save these credentials.";
            
        } catch (Exception $e) {
            if ($db->inTransaction()) {
                $db->rollback();
            }
            $error = 'Registration failed: ' . $e->getMessage();
            error_log("User registration error: " . $e->getMessage());
        }
    } else {
        $error = $validator->getFirstError();
    }
}


?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Registration - <?php echo SITE_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        .register-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .register-header {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="register-card">
                    <div class="register-header">
                        <i class="fas fa-user-plus fa-3x mb-3"></i>
                        <h3>User Registration</h3>
                        <p class="mb-0">Join Our MLM Network</p>
                    </div>
                    <div class="card-body p-4">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i><?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($success): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                                <div class="mt-3">
                                    <a href="login.php" class="btn btn-success">Login Now</a>
                                </div>
                            </div>
                        <?php else: ?>
                        
                        <form method="POST" action="">
                            <?php echo csrfTokenInput(); ?>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="full_name" class="form-label">
                                        <i class="fas fa-user me-2"></i>Full Name *
                                    </label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-2"></i>Email *
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">
                                        <i class="fas fa-phone me-2"></i>Phone Number *
                                    </label>
                                    <input type="tel" class="form-control" id="phone" name="phone" 
                                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="sponsor_id" class="form-label">
                                        <i class="fas fa-users me-2"></i>Sponsor ID *
                                    </label>
                                    <input type="text" class="form-control" id="sponsor_id" name="sponsor_id" 
                                           value="<?php echo htmlspecialchars($_POST['sponsor_id'] ?? $sponsorId); ?>" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">
                                    <i class="fas fa-map-marker-alt me-2"></i>Address
                                </label>
                                <textarea class="form-control" id="address" name="address" rows="2"><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">
                                    <i class="fas fa-sitemap me-2"></i>Placement Side *
                                </label>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="placement_side" id="left" value="left" 
                                                   <?php echo (($_POST['placement_side'] ?? '') === 'left') ? 'checked' : ''; ?> required>
                                            <label class="form-check-label" for="left">
                                                <i class="fas fa-arrow-left me-2"></i>Left Side
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="placement_side" id="right" value="right"
                                                   <?php echo (($_POST['placement_side'] ?? '') === 'right') ? 'checked' : ''; ?> required>
                                            <label class="form-check-label" for="right">
                                                <i class="fas fa-arrow-right me-2"></i>Right Side
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>Password *
                                    </label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                </div>
                                
                                <div class="col-md-6 mb-3">
                                    <label for="confirm_password" class="form-label">
                                        <i class="fas fa-lock me-2"></i>Confirm Password *
                                    </label>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                            </div>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    I agree to the <a href="#" target="_blank">Terms and Conditions</a>
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-info w-100 mb-3">
                                <i class="fas fa-user-plus me-2"></i>Register
                            </button>
                        </form>
                        
                        <?php endif; ?>
                        
                        <div class="text-center">
                            <p class="mb-2">Already have an account? <a href="login.php">Login here</a></p>
                            <a href="../index.php" class="text-muted">
                                <i class="fas fa-arrow-left me-2"></i>Back to Home
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

